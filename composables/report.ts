import { useQueryClient } from '@tanstack/vue-query'
import { <PERSON><PERSON><PERSON> } from 'buffer'
import { useNotifications } from './notifications'

export type ReportOption = {
    fileType: string
    fileOption: string
    fileExtension?: string
}

const MIME_TYPES: Record<string, string> = {
    'pdf': 'application/pdf',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'xls': 'application/vnd.ms-excel',
    'csv': 'text/csv'
}

export const useCreateReport = function () {
    const queryClient = useQueryClient()
    const { schedule } = useNotifications()
    const message = schedule()

    const getDocument = async (eventId: number, fileData: ReportOption) => {

        let queryKey = [`print-${eventId}-${fileData.fileType}`]
        let url = `/api/events/${eventId}/print?type=${fileData.fileOption}`
        let method = 'GET'
        let body: any = null

        if (fileData.fileType === 'COMMUNICATION_LIST' || fileData.fileType === 'NAME_LIST' || fileData.fileType === 'PARTICIPANTS_LIST') {
            queryKey = [`report-${eventId}-${fileData.fileType}`]
            url = `/api/events/${eventId}/report`
            method = 'POST'
            body = {
                reportType: fileData.fileType,
                fileType: fileData.fileExtension,
                dataBasis: fileData.fileOption
            }
        }

        if (fileData.fileType === 'SORTED_EXPORT') {
            queryKey = [`ordered-export-${eventId}-${fileData.fileType}`]
            url = `/api/events/${eventId}/registrations/ordered-export`
        }

        message.show('Deine Druckdatei wird heruntergeladen.', 'waiting')

        const fetchOptions: any = {
            method,
            responseType: 'blob'
        }

        if (method === 'POST' && body) {
            fetchOptions.body = body
        }

        const response: Blob = await queryClient.fetchQuery({
            queryKey,
            queryFn: async () => {
                return await $fetch(url, fetchOptions)
            }
        })

        if (response.size === 0) {
            message.hide()
            message.show('Das hat nicht geklappt. Zu deinem auswählten Report gibt es keine Daten in diesem Ereignis.')
            return
        }

        if (response instanceof Blob) {
            message.makeSuccess('Das hat geklappt: Du hast die Druckdatei deines Ereignisses heruntergeladen.')
            const buffer = await response.text()
            console.log("🚀 ~ getDocument ~ buffer:", buffer)

            const fileExtension = fileData.fileExtension?.toLowerCase() || 'pdf'
            const mimeType = MIME_TYPES[fileExtension] || 'application/pdf'
            return 'data:' + mimeType + ';base64,' + buffer.toString()
        }

        message.makeError('Bei der Erstellung der Dokumentation ist ein Fehler aufgetreten.')
    }

    return {
        getDocument
    }
}
